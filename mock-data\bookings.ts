import {
  BookingData,
  BookingDetails,
  MyBookingInfo,
  MybookingsRes,
  PaymentDetails,
} from "@/app/types/CommonComponent.types";
import { mockTournamentDetails, mockTimeSlots } from "./tournaments";

// Mock Payment Details
export const mockPaymentDetails: PaymentDetails[] = [
  {
    amount: "249.82",
    payment_method: "UPI",
    created_at: "2024-01-10T10:30:00Z",
  },
  {
    amount: "187.82",
    payment_method: "Credit Card",
    created_at: "2024-01-11T14:15:00Z",
  },
  {
    amount: "372.82",
    payment_method: "Debit Card",
    created_at: "2024-01-12T09:45:00Z",
  },
  {
    amount: "199.00",
    payment_method: "Wallet",
    created_at: "2024-01-13T16:20:00Z",
  },
];

// Mock Tournament Bookings Info (for SelectScheduleCard)
export const mockTournamentBookingsInfo = [
  {
    slot_id: 1,
    booking_id: "book_001",
    game_link: "https://game.example.com/room/abc123",
    room_id: "ROOM123ABC",
    room_password: "PASS123",
    in_game_name: "ProGamer2024",
  },
  {
    slot_id: 2,
    booking_id: "book_002",
    game_link: "https://game.example.com/room/def456",
    room_id: "ROOM456DEF",
    room_password: "PASS456",
    in_game_name: "ElitePlayer",
  },
  {
    slot_id: 3,
    booking_id: "book_003",
    game_link: "https://game.example.com/room/ghi789",
    room_id: "ROOM789GHI",
    room_password: "PASS789",
    in_game_name: "ChampionX",
  },
  {
    slot_id: 4,
    booking_id: null, // No booking for this slot
    game_link: null,
    room_id: null,
    room_password: null,
    in_game_name: null,
  },
];

// Mock My Booking Info
export const mockMyBookingInfo: MyBookingInfo[] = [
  {
    booking_id: "book_001",
    tournament_name: "BGMI Championship Series",
    tournament_date: "2024-01-15",
    time_slot: "10:00 AM",
    in_game_name: "ProGamer2024",
    game_link: "https://game.example.com/room/abc123",
    room_id: "ROOM123ABC",
    room_password: "PASS123",
    result: "Winner",
    amount: "249.82",
    status: "completed",
    created_at: "2024-01-10T10:30:00Z",
  },
  {
    booking_id: "book_002",
    tournament_name: "Free Fire Masters",
    tournament_date: "2024-01-16",
    time_slot: "2:00 PM",
    in_game_name: "ElitePlayer",
    game_link: "https://game.example.com/room/def456",
    room_id: "ROOM456DEF",
    room_password: "PASS456",
    result: "Runner-up",
    amount: "187.82",
    status: "completed",
    created_at: "2024-01-11T14:15:00Z",
  },
  {
    booking_id: "book_003",
    tournament_name: "Call of Duty Mobile Pro League",
    tournament_date: "2024-01-17",
    time_slot: "6:00 PM",
    in_game_name: "ChampionX",
    game_link: "https://game.example.com/room/ghi789",
    room_id: "ROOM789GHI",
    room_password: "PASS789",
    result: "3rd Place",
    amount: "372.82",
    status: "completed",
    created_at: "2024-01-12T09:45:00Z",
  },
  {
    booking_id: "book_004",
    tournament_name: "PUBG Mobile World Cup",
    tournament_date: "2024-01-18",
    time_slot: "10:00 PM",
    in_game_name: "WarriorKing",
    game_link: null,
    room_id: null,
    room_password: null,
    result: "",
    amount: "199.00",
    status: "upcoming",
    created_at: "2024-01-13T16:20:00Z",
  },
  {
    booking_id: "book_005",
    tournament_name: "Valorant Ignition Series",
    tournament_date: "2024-01-19",
    time_slot: "4:00 PM",
    in_game_name: "TacticalAce",
    game_link: null,
    room_id: null,
    room_password: null,
    result: "",
    amount: "299.00",
    status: "cancelled",
    created_at: "2024-01-14T11:30:00Z",
  },
  {
    booking_id: "book_006",
    tournament_name: "Clash Royale Arena",
    tournament_date: "2024-01-20",
    time_slot: "12:00 PM",
    in_game_name: "CardMaster",
    game_link: null,
    room_id: null,
    room_password: null,
    result: "Eliminated",
    amount: "149.00",
    status: "completed",
    created_at: "2024-01-15T08:45:00Z",
  },
];

// Mock My Bookings Response (with pagination)
export const mockMyBookingsResponse: MybookingsRes = {
  count: mockMyBookingInfo.length,
  next: null,
  previous: null,
  results: mockMyBookingInfo,
};

// Mock Booking Data (for booking success)
export const mockBookingData: BookingData[] = [
  {
    booking_id: "book_001",
    tournament: mockTournamentDetails[0],
    time_slot: mockTimeSlots[0],
    payment: mockPaymentDetails[0],
    created_at: "2024-01-10T10:30:00Z",
  },
  {
    booking_id: "book_002",
    tournament: mockTournamentDetails[1],
    time_slot: mockTimeSlots[1],
    payment: mockPaymentDetails[1],
    created_at: "2024-01-11T14:15:00Z",
  },
  {
    booking_id: "book_003",
    tournament: mockTournamentDetails[2],
    time_slot: mockTimeSlots[2],
    payment: mockPaymentDetails[2],
    created_at: "2024-01-12T09:45:00Z",
  },
];

// Mock Booking Details (for individual booking page)
export const mockBookingDetails: BookingDetails[] = [
  {
    booking_id: "book_001",
    in_game_name: "ProGamer2024",
    tournament: mockTournamentDetails[0],
    time_slot: mockTimeSlots[0],
    payment: mockPaymentDetails[0],
    created_at: "2024-01-10T10:30:00Z",
    result: "Winner",
    game_link: "https://game.example.com/room/abc123",
    room_id: "ROOM123ABC",
    room_password: "PASS123",
    status: "completed",
    amount: "249.82",
  },
  {
    booking_id: "book_002",
    in_game_name: "ElitePlayer",
    tournament: mockTournamentDetails[1],
    time_slot: mockTimeSlots[1],
    payment: mockPaymentDetails[1],
    created_at: "2024-01-11T14:15:00Z",
    result: "Runner-up",
    game_link: "https://game.example.com/room/def456",
    room_id: "ROOM456DEF",
    room_password: "PASS456",
    status: "completed",
    amount: "187.82",
  },
  {
    booking_id: "book_003",
    in_game_name: "ChampionX",
    tournament: mockTournamentDetails[2],
    time_slot: mockTimeSlots[2],
    payment: mockPaymentDetails[2],
    created_at: "2024-01-12T09:45:00Z",
    result: "3rd Place",
    game_link: "https://game.example.com/room/ghi789",
    room_id: "ROOM789GHI",
    room_password: "PASS789",
    status: "completed",
    amount: "372.82",
  },
  {
    booking_id: "book_004",
    in_game_name: "WarriorKing",
    tournament: mockTournamentDetails[0],
    time_slot: mockTimeSlots[3],
    payment: mockPaymentDetails[3],
    created_at: "2024-01-13T16:20:00Z",
    result: null,
    game_link: null,
    room_id: null,
    room_password: null,
    status: "upcoming",
    amount: "199.00",
  },
];

// Helper function to get booking by ID
export const getBookingById = (bookingId: string): BookingDetails | undefined => {
  return mockBookingDetails.find(booking => booking.booking_id === bookingId);
};

// Helper function to get tournament bookings info by tournament ID
export const getTournamentBookingsInfo = (tournamentId: string) => {
  // Return mock data for any tournament ID
  return mockTournamentBookingsInfo;
};
