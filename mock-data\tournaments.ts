import {
  Tournament,
  TournamentCategory,
  TournamentDetails,
  TimeSlot,
  Payment,
} from "@/app/types/CommonComponent.types";

// Mock Time Slots
export const mockTimeSlots: TimeSlot[] = [
  {
    id: 1,
    time: "2024-01-15T10:00:00Z",
    formatted_time: "10:00 AM",
    bookings_count: 45,
    max_players: 100,
    is_expired: false,
    is_cancelled: false,
    cancel_reason: null,
  },
  {
    id: 2,
    time: "2024-01-15T14:00:00Z",
    formatted_time: "2:00 PM",
    bookings_count: 78,
    max_players: 100,
    is_expired: false,
    is_cancelled: false,
    cancel_reason: null,
  },
  {
    id: 3,
    time: "2024-01-15T18:00:00Z",
    formatted_time: "6:00 PM",
    bookings_count: 92,
    max_players: 100,
    is_expired: false,
    is_cancelled: false,
    cancel_reason: null,
  },
  {
    id: 4,
    time: "2024-01-15T22:00:00Z",
    formatted_time: "10:00 PM",
    bookings_count: 100,
    max_players: 100,
    is_expired: false,
    is_cancelled: false,
    cancel_reason: null,
  },
  {
    id: 5,
    time: "2024-01-14T20:00:00Z",
    formatted_time: "8:00 PM",
    bookings_count: 25,
    max_players: 50,
    is_expired: true,
    is_cancelled: false,
    cancel_reason: null,
  },
  {
    id: 6,
    time: "2024-01-16T16:00:00Z",
    formatted_time: "4:00 PM",
    bookings_count: 0,
    max_players: 100,
    is_expired: false,
    is_cancelled: true,
    cancel_reason: "Technical issues with server",
  },
];

// Mock Payment Details
export const mockPayment: Payment = {
  join_price: "199",
  tax_amount: 35.82,
  platform_fees: 15.0,
  final_amount: 249.82,
};

// Mock Tournaments
export const mockTournaments: Tournament[] = [
  {
    tournament_id: "tour_001",
    name: "BGMI Championship Series",
    description: "Ultimate battle royale tournament with massive prize pool",
    image: "/images/tournaments/bgmi-championship.jpg",
    prize_pool: 50000,
    date: "2024-01-15",
  },
  {
    tournament_id: "tour_002",
    name: "Free Fire Masters",
    description: "Fast-paced action tournament for Free Fire enthusiasts",
    image: "/images/tournaments/free-fire-masters.jpg",
    prize_pool: 25000,
    date: "2024-01-16",
  },
  {
    tournament_id: "tour_003",
    name: "Call of Duty Mobile Pro League",
    description: "Professional COD Mobile tournament with top players",
    image: "/images/tournaments/cod-mobile-pro.jpg",
    prize_pool: 75000,
    date: "2024-01-17",
  },
  {
    tournament_id: "tour_004",
    name: "PUBG Mobile World Cup",
    description: "Global PUBG Mobile championship event",
    image: "/images/tournaments/pubg-world-cup.jpg",
    prize_pool: 100000,
    date: "2024-01-18",
  },
  {
    tournament_id: "tour_005",
    name: "Valorant Ignition Series",
    description: "Tactical FPS tournament for Valorant players",
    image: "/images/tournaments/valorant-ignition.jpg",
    prize_pool: 30000,
    date: "2024-01-19",
  },
  {
    tournament_id: "tour_006",
    name: "Clash Royale Arena",
    description: "Strategic card battle tournament",
    image: "/images/tournaments/clash-royale.jpg",
    prize_pool: 15000,
    date: "2024-01-20",
  },
];

// Mock Tournament Categories
export const mockTournamentCategories: TournamentCategory[] = [
  {
    type: "Battle Royale",
    tournaments: [mockTournaments[0], mockTournaments[1], mockTournaments[3]],
  },
  {
    type: "FPS",
    tournaments: [mockTournaments[2], mockTournaments[4]],
  },
  {
    type: "Strategy",
    tournaments: [mockTournaments[5]],
  },
  {
    type: "Featured",
    tournaments: [mockTournaments[0], mockTournaments[2], mockTournaments[3]],
  },
];

// Mock Tournament Details
export const mockTournamentDetails: TournamentDetails[] = [
  {
    tournament_id: "tour_001",
    date: "2024-01-15",
    prize_pool: 50000,
    join_price: 199,
    bookings_count: 215,
    max_players: 400,
    map: "Erangel",
    mode: "Squad",
    time_slots: mockTimeSlots.slice(0, 4),
    payment: mockPayment,
    name: "BGMI Championship Series",
    description: "Ultimate battle royale tournament with massive prize pool. Join the most competitive BGMI tournament and showcase your skills against the best players.",
    image: "/images/tournaments/bgmi-championship.jpg",
    first_prize: 20000,
    second_prize: 15000,
    third_prize: 10000,
    per_kill_prize: 100,
    created_by: {
      name: "GameMaster Pro",
      youtube_link: "https://youtube.com/@gamemasterpro",
      image: "/images/creators/gamemaster-pro.jpg",
    },
  },
  {
    tournament_id: "tour_002",
    date: "2024-01-16",
    prize_pool: 25000,
    join_price: 149,
    bookings_count: 156,
    max_players: 200,
    map: "Bermuda",
    mode: "Squad",
    time_slots: mockTimeSlots.slice(1, 4),
    payment: {
      join_price: "149",
      tax_amount: 26.82,
      platform_fees: 12.0,
      final_amount: 187.82,
    },
    name: "Free Fire Masters",
    description: "Fast-paced action tournament for Free Fire enthusiasts. Experience intense battles and compete for amazing prizes.",
    image: "/images/tournaments/free-fire-masters.jpg",
    first_prize: 10000,
    second_prize: 7500,
    third_prize: 5000,
    per_kill_prize: 50,
    created_by: {
      name: "FireStorm Gaming",
      youtube_link: "https://youtube.com/@firestormgaming",
      image: "/images/creators/firestorm-gaming.jpg",
    },
  },
  {
    tournament_id: "tour_003",
    date: "2024-01-17",
    prize_pool: 75000,
    join_price: 299,
    bookings_count: 324,
    max_players: 500,
    map: "Nuketown",
    mode: "Team Deathmatch",
    time_slots: mockTimeSlots.slice(0, 5),
    payment: {
      join_price: "299",
      tax_amount: 53.82,
      platform_fees: 20.0,
      final_amount: 372.82,
    },
    name: "Call of Duty Mobile Pro League",
    description: "Professional COD Mobile tournament with top players from around the world. Prove your tactical skills and teamwork.",
    image: "/images/tournaments/cod-mobile-pro.jpg",
    first_prize: 30000,
    second_prize: 22500,
    third_prize: 15000,
    per_kill_prize: 150,
    created_by: {
      name: "TacticalOps",
      youtube_link: "https://youtube.com/@tacticalops",
      image: "/images/creators/tactical-ops.jpg",
    },
  },
];
