import { User } from "@/app/types/User";
import {
  LeaderboardData,
  LeaderboardRes,
  Transaction,
  TransactionsResponse,
} from "@/app/types/CommonComponent.types";

// Mock User Data
export const mockUser: User = {
  id: 1,
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-15T10:30:00Z",
  user_id: "user_12345",
  name: "<PERSON>",
  email: "<EMAIL>",
  phone: "+91 9876543210",
  is_active: true,
  wallet: 2500.75,
  redeem_wallet: 1200.50,
  upi_id: "john.doe@paytm",
  pan_number: "**********",
};

// Mock Additional Users for Leaderboard
export const mockUsers: User[] = [
  mockUser,
  {
    id: 2,
    created_at: "2024-01-02T00:00:00Z",
    updated_at: "2024-01-15T09:15:00Z",
    user_id: "user_12346",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+91 9876543211",
    is_active: true,
    wallet: 3200.25,
    redeem_wallet: 1800.75,
    upi_id: "jane.smith@gpay",
    pan_number: "**********",
  },
  {
    id: 3,
    created_at: "2024-01-03T00:00:00Z",
    updated_at: "2024-01-15T11:45:00Z",
    user_id: "user_12347",
    name: "Mike Johnson",
    email: "<EMAIL>",
    phone: "+91 9876543212",
    is_active: true,
    wallet: 1850.00,
    redeem_wallet: 950.25,
    upi_id: "mike.johnson@phonepe",
    pan_number: "**********",
  },
  {
    id: 4,
    created_at: "2024-01-04T00:00:00Z",
    updated_at: "2024-01-15T08:30:00Z",
    user_id: "user_12348",
    name: "Sarah Wilson",
    email: "<EMAIL>",
    phone: "+91 9876543213",
    is_active: true,
    wallet: 4100.50,
    redeem_wallet: 2200.00,
    upi_id: "sarah.wilson@paytm",
    pan_number: "**********",
  },
  {
    id: 5,
    created_at: "2024-01-05T00:00:00Z",
    updated_at: "2024-01-15T12:00:00Z",
    user_id: "user_12349",
    name: "Alex Brown",
    email: "<EMAIL>",
    phone: "+91 9876543214",
    is_active: true,
    wallet: 2750.25,
    redeem_wallet: 1350.75,
    upi_id: "alex.brown@gpay",
    pan_number: "**********",
  },
];

// Mock Leaderboard Data
export const mockLeaderboardData: LeaderboardData[] = [
  {
    user_id: "user_12348",
    name: "Sarah Wilson",
    total_winnings: 45000,
    rank: 1,
  },
  {
    user_id: "user_12346",
    name: "Jane Smith",
    total_winnings: 38500,
    rank: 2,
  },
  {
    user_id: "user_12349",
    name: "Alex Brown",
    total_winnings: 32000,
    rank: 3,
  },
  {
    user_id: "user_12345",
    name: "John Doe",
    total_winnings: 28750,
    rank: 4,
  },
  {
    user_id: "user_12347",
    name: "Mike Johnson",
    total_winnings: 25200,
    rank: 5,
  },
  {
    user_id: "user_12350",
    name: "Emma Davis",
    total_winnings: 22800,
    rank: 6,
  },
  {
    user_id: "user_12351",
    name: "Chris Taylor",
    total_winnings: 20500,
    rank: 7,
  },
  {
    user_id: "user_12352",
    name: "Lisa Anderson",
    total_winnings: 18900,
    rank: 8,
  },
  {
    user_id: "user_12353",
    name: "David Miller",
    total_winnings: 17200,
    rank: 9,
  },
  {
    user_id: "user_12354",
    name: "Amy Garcia",
    total_winnings: 15800,
    rank: 10,
  },
];

// Mock Leaderboard Response
export const mockLeaderboardResponse: LeaderboardRes = {
  count: mockLeaderboardData.length,
  next: null,
  previous: null,
  results: mockLeaderboardData,
};

// Mock Transaction Data
export const mockTransactions: Transaction[] = [
  {
    transaction_id: "txn_001",
    amount: "249.82",
    transaction_direction: "debit",
    description: "Tournament Entry - BGMI Championship Series",
    created_at: "2024-01-10T10:30:00Z",
  },
  {
    transaction_id: "txn_002",
    amount: "20000.00",
    transaction_direction: "credit",
    description: "Prize Money - BGMI Championship Series (1st Place)",
    created_at: "2024-01-15T18:00:00Z",
  },
  {
    transaction_id: "txn_003",
    amount: "187.82",
    transaction_direction: "debit",
    description: "Tournament Entry - Free Fire Masters",
    created_at: "2024-01-11T14:15:00Z",
  },
  {
    transaction_id: "txn_004",
    amount: "7500.00",
    transaction_direction: "credit",
    description: "Prize Money - Free Fire Masters (2nd Place)",
    created_at: "2024-01-16T19:30:00Z",
  },
  {
    transaction_id: "txn_005",
    amount: "372.82",
    transaction_direction: "debit",
    description: "Tournament Entry - Call of Duty Mobile Pro League",
    created_at: "2024-01-12T09:45:00Z",
  },
  {
    transaction_id: "txn_006",
    amount: "15000.00",
    transaction_direction: "credit",
    description: "Prize Money - Call of Duty Mobile Pro League (3rd Place)",
    created_at: "2024-01-17T20:15:00Z",
  },
  {
    transaction_id: "txn_007",
    amount: "1000.00",
    transaction_direction: "credit",
    description: "Wallet Top-up",
    created_at: "2024-01-08T16:20:00Z",
  },
  {
    transaction_id: "txn_008",
    amount: "500.00",
    transaction_direction: "debit",
    description: "Withdrawal to Bank Account",
    created_at: "2024-01-14T11:30:00Z",
  },
  {
    transaction_id: "txn_009",
    amount: "199.00",
    transaction_direction: "debit",
    description: "Tournament Entry - PUBG Mobile World Cup",
    created_at: "2024-01-13T16:20:00Z",
  },
  {
    transaction_id: "txn_010",
    amount: "50.00",
    transaction_direction: "credit",
    description: "Referral Bonus",
    created_at: "2024-01-09T12:45:00Z",
  },
];

// Mock Transactions Response
export const mockTransactionsResponse: TransactionsResponse = {
  count: mockTransactions.length,
  next: null,
  previous: null,
  results: mockTransactions,
};

// Helper function to get user by ID
export const getUserById = (userId: string): User | undefined => {
  return mockUsers.find(user => user.user_id === userId);
};

// Helper function to get current user
export const getCurrentUser = (): User => {
  return mockUser;
};
